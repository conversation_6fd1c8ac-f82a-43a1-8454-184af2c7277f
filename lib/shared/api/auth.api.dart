import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/perfil_offline.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/shared/api/virtual-card.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/loggin_no_password.vo.dart';
import 'package:cliente_minha_unimed/shared/api/vo/version-validate.vo.dart';
import 'package:cliente_minha_unimed/shared/api/websocket.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/flavor-config.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/services/version.service.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:evaluation/evaluation.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:http_client/http_client.dart';
//import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:intl/intl.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthApi {
  final UnimedHttpClient httpClient;
  final attributeCredentials = 'cookie-session';
  final passwordCredentials = '357538782F413F4428472B4B62506553';
  UserCredentials? _credentials;
  String? _atendimentoId;
  String? _tokenPerfilApps;
  String? fcmToken;
  late User _user;

  late String _userToken;
  String get userToken => _userToken;

  String? get atendimentoId => _atendimentoId;
  User get userLogged => _user;

  final logger = UnimedLogger(className: 'AuthApi');

  AuthApi(this.httpClient);

  Future<User> login(UserCredentials credentials) async {
    UserCredentials? userCredentials;
    _userToken = const String.fromEnvironment('TOKEN_FORCE', defaultValue: "");
    if (_userToken.isEmpty) {
      userCredentials = credentials;
      _userToken = await createToken(credentials: credentials);
    } else {
      userCredentials = UserCredentials(
        cpf: const String.fromEnvironment('CPF_FORCE', defaultValue: ""),
        password: credentials.password,
      );
    }

    _setCredentials(userCredentials);
    _setAtendimentoId(userCredentials);

    final int platform = Platform.isIOS ? 1 : 3;

    try {
      if (platform != 1 && platform != 3) {
        logger.e('device not found: $platform');
        throw AuthException('O tipo de dispositivo não é aceito');
      }

      await _setPerfilAppsBaseUrl(login: credentials.cpf11Digits);

      //Nao funciona em simulador IOS https://firebase.flutter.dev/docs/messaging/usage/#receiving-messages
      try {
        fcmToken = await FirebaseMessaging.instance.getToken();
      } catch (e) {
        logger.e('login FCM Token Error');
      }
      logger.d('login FCM Token: $fcmToken');

      final versionInfo =
          await (Locator.instance.get<VersionService>().getInfo());

      logger.d(
          'login BaseUrl: ${FlavorConfig.instance!.values.profilePermissions.url}');
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}auth/v2/login-app';

      final body = jsonEncode({
        "t": _userToken,
        "i": fcmToken,
        "y": platform,
        "v": versionInfo.version,
        "u": credentials.cpf11Digits,
      });

      String token = await tokenPerfilApps();
      String bearer = 'Bearer $token';

      final response = await this.httpClient.post(
        Uri.parse(url),
        body: body,
        headers: {
          'Authorization': '$bearer',
          "Content-Type": "application/json",
        },
      );

      if (response.statusCode == 200) {
        _user = User.fromJson(jsonDecode(response.body)['retorno']);

        credentials.cpf = _user.login;
        Locator.instance
            .get<RemoteLog>()
            .setUserId(credentials.cpfClean); // setando CPF no remotelog

        // FirebaseCrashlytics.instance.setUserIdentifier(
        //     userCredentials.cpfClean); // setando CPF no Crashlytics

        Perfil _profile = _user.getPerfilByCpf(credentials.cpf);

        await _saveOfflinePerfil();

        Locator.instance.get<RemoteLog>().setCard(_profile
            .carteira!.carteiraNumero); // setando a carteira da pessoa logada

        Locator.instance
            .get<RemoteLog>()
            .setAtendimentoId(_atendimentoId!); // Setando Atendimento ID
        AnalyticsService().setUserId(cpf: credentials.cpf);

        Locator.instance.get<Evaluation>().setUserId(credentials.cpfClean);
        Locator.instance.get<Evaluation>().setAtendimentoId(_atendimentoId!);

        return _user;
      } else {
        logger.e(
            'login statusCode : ${response.statusCode} message : ${response.reasonPhrase} => ${response.body}');

        final data = jsonDecode(response.body);
        final _message =
            data['mensagem'] ?? data['message'] ?? MessageException.GENERAL;

        throw AuthException(_message);
      }
    } on ServiceTimeoutException catch (e) {
      logger.e('Error on login method ServiceTimeoutException ${e.message}');
      throw AuthException(e.message);
    } on NoInternetException catch (e) {
      logger.e('Error on login method NoInternetException ${e.message}');
      throw AuthException(e.message);
    } on AuthException catch (e) {
      logger.e('Error on login method AuthException => $e');
      throw e;
    } on FormatException catch (e) {
      logger.e('Error on login method FormatException => $e');
      throw AuthException(MessageException.GENERAL);
    } on SocketException catch (e) {
      logger.e('Error on login method SocketException => $e');
      throw AuthException('Erro de conexão. Verifique sua internet e tente novamente.');
    } on TimeoutException catch (e) {
      logger.e('Error on login method TimeoutException => $e');
      throw AuthException('Tempo limite excedido. Tente novamente.');
    } on Exception catch (e) {
      logger.e('Error on login method general Exception => $e');
      throw AuthException(MessageException.GENERAL);
    } catch (e) {
      logger.e('Error on login method unexpected error => $e');
      throw AuthException(MessageException.GENERAL);
    }
  }

  _setPerfilAppsBaseUrl({required String login}) async {
    try {
      final ref = FirebaseDatabase.instance.ref();
      final snapshot = await ref.child(login).get();
      if (snapshot.exists) {
        final data = snapshot.value! as Map;

        if (data[FlavorConfig.instance!.name]?['perfilapps_url'] != null) {
          logger.d(
              'setPerfilAppsBaseUrl - Setting base profilePermissions url to: ${data[FlavorConfig.instance!.name]?['perfilapps_url']}');
          FlavorConfig.instance!.values.profilePermissions.url =
              "${data[FlavorConfig.instance!.name]['perfilapps_url']}/api/";
          FlavorConfig.instance!.values.graphql.url =
              "${data[FlavorConfig.instance!.name]['perfilapps_url']}/graphql";

          logger.d(
              'setPerfilAppsBaseUrl - profilePermissions url: ${FlavorConfig.instance!.values.profilePermissions.url}');
        } else {
          logger.d(
              'setPerfilAppsBaseUrl - No data available for key ${FlavorConfig.instance!.name}');
        }
      } else {
        logger.d('setPerfilAppsBaseUrl - No data available to login $login');
      }
    } catch (e) {
      logger.d('setPerfilAppsBaseUrl - error ${e}');
    }
  }

  Future<void> _saveOfflinePerfil() async {
    final _prefs = await SharedPreferences.getInstance();
    final _sufix = FlavorConfig.isProduction() ? '_prod' : '_dev';
    final _key = 'perfisOffline_$_sufix';
    final List<Perfil> _perfisOffline = [];

    _user.perfis.forEach((perfil) {
      _perfisOffline.add(perfil);
    });

    final _perfilOffline = PerfilOfflineModel(list: _perfisOffline);

    await _prefs.remove(_key);
    await _prefs.setString(_key, jsonEncode(_perfilOffline.toJson()));

    String? current = _user.config.virtualCard!.current;
    String url = current != null && current.isNotEmpty
        ? (_user.config.virtualCard!.toJson())[current]
        : _user.config.virtualCard!.urlTokenV1!;
    Locator.instance.get<VirtualCardApi>().saveTokenUrl(url);
  }

  Future<User> loginByToken(LogginNoPasswordVO logginNoPasswordVO) async {
    _userToken = logginNoPasswordVO.token;

    UserCredentials userCredentials =
        UserCredentials(cpf: logginNoPasswordVO.cpf);
    _setCredentials(userCredentials);
    _setAtendimentoId(userCredentials);

    final int platform = Platform.isIOS ? 1 : 3;

    try {
      if (platform != 1 && platform != 3) {
        logger.e('device not found: $platform');
        throw AuthException('O tipo de dispositivo não é aceito');
      }

      try {
        fcmToken = await FirebaseMessaging.instance.getToken();
      } catch (e) {
        logger.e('login FCM Token Error');
      }
      logger.d('login FCM Token: $fcmToken');

      final versionInfo =
          await (Locator.instance.get<VersionService>().getInfo());

      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}auth/login-app?t=$_userToken&i=${fcmToken}&y=$platform&v=${versionInfo.version}&u=${userCredentials.cpf11Digits}';

      final response = await this
          .httpClient
          .get(Uri.parse(url), headers: {"Content-Type": "application/json"});

      if (response.statusCode == 200) {
        _user = User.fromJson(jsonDecode(response.body)['retorno']);

        userCredentials.cpf = _user.login;
        Locator.instance
            .get<RemoteLog>()
            .setUserId(userCredentials.cpfClean); // setando CPF no remotelog

        // FirebaseCrashlytics.instance.setUserIdentifier(
        //     userCredentials.cpfClean); // setando CPF no Crashlytics

        Perfil _profile = _user.getPerfilByCpf(userCredentials.cpf);

        Locator.instance.get<RemoteLog>().setCard(_profile
            .carteira!.carteiraNumero); // setando a carteira da pessoa logada

        Locator.instance
            .get<RemoteLog>()
            .setAtendimentoId(_atendimentoId!); // Setando Atendimento ID
        AnalyticsService().setUserId(cpf: userCredentials.cpf);

        Locator.instance.get<Evaluation>().setUserId(userCredentials.cpfClean);
        Locator.instance.get<Evaluation>().setAtendimentoId(_atendimentoId!);

        return _user;
      } else {
        logger.e(
            'loginByToken statusCode : ${response.statusCode} message : ${response.reasonPhrase} => ${response.body}');

        try {
          final data = jsonDecode(response.body);
          final _message =
              data['mensagem'] ?? data['message'] ?? MessageException.GENERAL;
          throw AuthException(_message);
        } catch (e) {
          logger.e('Error parsing response body in loginByToken => $e');
          throw AuthException(MessageException.GENERAL);
        }
      }
    } on ServiceTimeoutException catch (e) {
      logger.e('Error on loginByToken method ServiceTimeoutException ${e.message}');
      throw AuthException(e.message);
    } on NoInternetException catch (e) {
      logger.e('Error on loginByToken method NoInternetException ${e.message}');
      throw AuthException(e.message);
    } on AuthException catch (e) {
      logger.e('Error on loginByToken method AuthException => $e');
      throw e;
    } on FormatException catch (e) {
      logger.e('Error on loginByToken method FormatException => $e');
      throw AuthException(MessageException.GENERAL);
    } on SocketException catch (e) {
      logger.e('Error on loginByToken method SocketException => $e');
      throw AuthException('Erro de conexão. Verifique sua internet e tente novamente.');
    } on TimeoutException catch (e) {
      logger.e('Error on loginByToken method TimeoutException => $e');
      throw AuthException('Tempo limite excedido. Tente novamente.');
    } on Exception catch (e) {
      logger.e('Error on loginByToken method general Exception => $e');
      throw AuthException(MessageException.GENERAL);
    } catch (e) {
      logger.e('Error on loginByToken method unexpected error => $e');
      throw AuthException(MessageException.GENERAL);
    }
  }

  Future<void> logout(UserCredentials credentials, String? deviceId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    final data = prefs.getString(attributeCredentials)!;

    logger.d('data user logout: $data');

    AnalyticsService().sendEvent(name: 'logout', parameters: {data: data});
    _logoutPerfilApps(credentials, deviceId);

    // Clean remote log data
    Locator.instance.get<RemoteLog>().cleanUserId();
    Locator.instance.get<RemoteLog>().cleanCard();
    Locator.instance.get<RemoteLog>().cleanAtendimentoId();

    // Disconnect websocket
    Locator.instance.get<WebSocketApi>().disconnectWebsocket();

    if (data.isNotEmpty) {
      prefs.remove(attributeCredentials);
    }

    //_credentials = null;
    _atendimentoId = null;
    //_user = null;
    FirebaseMessaging.instance.deleteToken();
  }

  void _logoutPerfilApps(UserCredentials credentials, String? deviceId) async {
    try {
      final token = _userToken;
      final versionInfo =
          await Locator.instance.get<VersionService>().getInfo();

      String bearerToken = await tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}auth/v2/logout-app?t=$token&i=${deviceId}&v=${versionInfo.version}&u=${credentials.cpf11Digits}';

      httpClient.get(Uri.parse(url), headers: {
        "Authorization": 'Bearer $bearerToken',
        "Content-Type": "application/json"
      });
    } catch (e) {
      logger.e('Exception on _logoutPerfilApps => $e');
    }
  }

  /// Recupera as credenciais do usuário para uso na biometria
  Future<UserCredentials?> getCredentials() async {
    if (FlavorConfig.isTest()) {
      return _credentials != null
          ? _credentials
          : UserCredentials(cpf: '54749212334', password: '123456');
    } else {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      final dataCrypted = prefs.getString(attributeCredentials);

      if (dataCrypted != null && dataCrypted.isNotEmpty) {
        final key = Key.fromUtf8(passwordCredentials);
        final iv = IV.fromLength(16);
        final encrypter = Encrypter(AES(key));

        final encrypted = Encrypted.fromBase64(dataCrypted);
        final data = encrypter.decrypt(encrypted, iv: iv);

        final jsonData = json.decode(data);

        return UserCredentials(
            cpf: jsonData['cpf'], password: jsonData['password']);
      } else {
        return null;
      }
    }
  }

  /// Salva as credenciais do usuário, para ser usado na biometria
  Future<void> _setCredentials(UserCredentials credentials) async {
    if (!FlavorConfig.isTest()) {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      final key = Key.fromUtf8(passwordCredentials);
      final iv = IV.fromLength(16);
      final encrypter = Encrypter(AES(key));

      Map<String, dynamic> jsonToSave = {
        'cpf': credentials.cpf,
        'password': credentials.password
      };

      final jsonString = json.encode(jsonToSave);
      final encrypted = encrypter.encrypt(jsonString, iv: iv);

      await prefs.setString(attributeCredentials, encrypted.base64);
    } else {
      _credentials = credentials;
    }
  }

  _setAtendimentoId(UserCredentials credentials) {
    if (_atendimentoId == null)
      _atendimentoId =
          '${credentials.cpfClean}-${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Pass user and password if not default values in flavor
  Future<String> tokenPerfilApps({String? user, String? password}) async {
    if (!_isValidTokenPerfilApps(_tokenPerfilApps)) {
      if (user == null)
        user = FlavorConfig.instance!.values.profilePermissions.user;
      if (password == null)
        password = FlavorConfig.instance!.values.profilePermissions.password;

      try {
        final String url =
            '${FlavorConfig.instance!.values.profilePermissions.url}auth/login';
        final body = jsonEncode({"user": user, "password": password});

        final headers = {"Content-Type": "application/json"};

        final response = await this.httpClient.post(Uri.parse(url),
            body: body, headers: headers, encodeBody: true, logBody: false);

        if (response.statusCode == 200) {
          logger.d('AuthApi tokenPerfilApps success logged');
          _tokenPerfilApps = (jsonDecode(response.body))['token'];
          logger.d('AuthApi tokenPerfilApps token $_tokenPerfilApps');

          return _tokenPerfilApps!;
        } else {
          logger.e('AuthApi tokenPerfilApps error status != 200 ${response.body}');

          try {
            final data = jsonDecode(response.body);
            final _message =
                data['mensagem'] ?? data['message'] ?? MessageException.GENERAL;
            throw ProfilesException(_message);
          } catch (e) {
            logger.e('Error parsing response body in tokenPerfilApps => $e');
            throw ProfilesException(MessageException.GENERAL);
          }
        }
      } on ServiceTimeoutException catch (e) {
        logger.e('AuthApi tokenPerfilApps ServiceTimeoutException => $e');
        throw ProfilesException(e.message);
      } on NoInternetException catch (e) {
        logger.e('AuthApi tokenPerfilApps NoInternetException => $e');
        throw ProfilesException(e.message);
      } on ProfilesException catch (e) {
        logger.e('AuthApi tokenPerfilApps ProfilesException => $e');
        throw e;
      } on FormatException catch (e) {
        logger.e('AuthApi tokenPerfilApps FormatException => $e');
        throw ProfilesException(MessageException.GENERAL);
      } on SocketException catch (e) {
        logger.e('AuthApi tokenPerfilApps SocketException => $e');
        throw ProfilesException('Erro de conexão. Verifique sua internet e tente novamente.');
      } on TimeoutException catch (e) {
        logger.e('AuthApi tokenPerfilApps TimeoutException => $e');
        throw ProfilesException('Tempo limite excedido. Tente novamente.');
      } catch (e) {
        logger.e('AuthApi tokenPerfilApps unexpected error => $e');
        throw ProfilesException(MessageException.GENERAL);
      }
    } else {
      return _tokenPerfilApps!;
    }
  }

  bool _isValidTokenPerfilApps(String? token) {
    if (_tokenPerfilApps == null || _tokenPerfilApps!.isEmpty) {
      return false;
    } else {
      final parts = token!.split('.');
      if (parts.length != 3) {
        throw Exception('invalid token');
      }

      final payload = _decodeBase64(parts[1]);
      final payloadMap = json.decode(payload);
      if (payloadMap is! Map<String, dynamic>) {
        throw Exception('invalid payload');
      }

      final DateTime exp =
          new DateTime.fromMillisecondsSinceEpoch(payloadMap['exp']);

      return exp.isAfter(DateTime.now());
    }
  }

  String _decodeBase64(String str) {
    String output = str.replaceAll('-', '+').replaceAll('_', '/');

    switch (output.length % 4) {
      case 0:
        break;
      case 2:
        output += '==';
        break;
      case 3:
        output += '=';
        break;
      default:
        throw Exception('Illegal base64url string!"');
    }

    return utf8.decode(base64Url.decode(output));
  }

  Future<VersionValidateResponse> checkVersionValidade({
    required String version,
    required String buildNumber,
  }) async {
    try {
      final token = await tokenPerfilApps(
          user: FlavorConfig.instance!.values.profilePermissions.user,
          password: FlavorConfig.instance!.values.profilePermissions.password);

      final _version = version.trim();
      final _buildNumber = buildNumber.trim();

      final String url =
          '${FlavorConfig.instance!.values.profilePermissions.url}version/cliente/$_version/$_buildNumber';

      final headers = {
        "Content-Type": "application/json",
        "Authorization": 'Bearer $token'
      };

      final response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        logger.d('AuthApi checkVersionValidade response => $data');

        final versionValidadeResponse = VersionValidateResponse.fromJson(data);
        versionValidadeResponse.isOutOfDate = false;

        return versionValidadeResponse;
      } else if (response.statusCode == 500) {
        final data = jsonDecode(response.body);
        logger.d('AuthApi checkVersionValidade response => $data');

        if (data['lastVersion'] != null) {
          final versionValidadeResponse =
              VersionValidateResponse.fromJson(data);
          versionValidadeResponse.isOutOfDate = true;

          return versionValidadeResponse;
        } else
          throw UnimedException(
              'AuthApi checkVersionValidade != 200 => ${response.statusCode}');
      } else {
        logger.e(
            'AuthApi checkVersionValidade error status != 200 ${response.body}');
        throw UnimedException(
            'AuthApi checkVersionValidade != 200 => ${response.statusCode}');
      }
    } on ServiceTimeoutException catch (e) {
      logger.e('AuthApi checkVersionValidade ServiceTimeoutException => $e');
      throw UnimedException(e.message);
    } on NoInternetException catch (e) {
      logger.e('AuthApi checkVersionValidade NoInternetException => $e');
      throw UnimedException(e.message);
    } on UnimedException catch (e) {
      throw e;
    } on FormatException catch (e) {
      logger.e('AuthApi checkVersionValidade FormatException => $e');
      throw UnimedException(MessageException.GENERAL);
    } on SocketException catch (e) {
      logger.e('AuthApi checkVersionValidade SocketException => $e');
      throw UnimedException('Erro de conexão. Verifique sua internet e tente novamente.');
    } on TimeoutException catch (e) {
      logger.e('AuthApi checkVersionValidade TimeoutException => $e');
      throw UnimedException('Tempo limite excedido. Tente novamente.');
    } catch (e) {
      logger.e('AuthApi checkVersionValidade unexpected error => $e');
      throw UnimedException(MessageException.GENERAL);
    }
  }

  Future<LogginNoPasswordVO> getLoginToken({
    required String qrCode,
  }) async {
    try {
      final token = await tokenPerfilApps(
          user: FlavorConfig.instance!.values.profilePermissions.user,
          password: FlavorConfig.instance!.values.profilePermissions.password);
      final String url =
          '${FlavorConfig.instance!.values.profilePermissions.url}auth/login-app/no-password/$qrCode';

      final headers = {
        "Content-Type": "application/json",
        "Authorization": 'Bearer $token'
      };

      final response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        logger.d('AuthApi getLoginToken response => $data');

        final logginNoPasswordVO = LogginNoPasswordVO.fromJson(data);

        return logginNoPasswordVO;
      } else if (response.statusCode == 500) {
        final data = jsonDecode(response.body);
        logger.d('AuthApi getLoginToken response => $data');

        throw UnimedException(data['message'] ?? MessageException.GENERAL);
      } else {
        logger.e('AuthApi getLoginToken error status != 200 ${response.body}');
        throw UnimedException(MessageException.GENERAL);
      }
    } on ServiceTimeoutException catch (e) {
      logger.e('AuthApi getLoginToken ServiceTimeoutException => $e');
      throw UnimedException(e.message);
    } on NoInternetException catch (e) {
      logger.e('AuthApi getLoginToken NoInternetException => $e');
      throw UnimedException(e.message);
    } on UnimedException catch (e) {
      throw e;
    } on FormatException catch (e) {
      logger.e('AuthApi getLoginToken FormatException => $e');
      throw UnimedException(MessageException.GENERAL);
    } on SocketException catch (e) {
      logger.e('AuthApi getLoginToken SocketException => $e');
      throw UnimedException('Erro de conexão. Verifique sua internet e tente novamente.');
    } on TimeoutException catch (e) {
      logger.e('AuthApi getLoginToken TimeoutException => $e');
      throw UnimedException('Tempo limite excedido. Tente novamente.');
    } catch (e) {
      logger.e('AuthApi getLoginToken unexpected error => $e');
      throw UnimedException(MessageException.GENERAL);
    }
  }

  Future<String> createToken({UserCredentials? credentials}) async {
    // verification
    if (credentials == null) return '';

    final today = DateFormat('dd/MM/y').format(DateTime.now());

    return md5
        .convert(utf8
            .encode('${credentials.cpfSabius}|${credentials.password}|$today'))
        .toString();
  }
}
