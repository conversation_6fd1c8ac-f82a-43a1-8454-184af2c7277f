// ignore_for_file: invalid_use_of_visible_for_testing_member

import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/indicators/indicators_event.dart';
import 'package:cliente_minha_unimed/bloc/res/indicators/indicators_state.dart';
import 'package:cliente_minha_unimed/models/res/res_indicator_model.dart';
import 'package:cliente_minha_unimed/shared/api/res.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';

class ResIndicatorBloc extends Bloc<ResIndicatorEvent, ResIndicatorsState> {
  ResIndicatorBloc() : super(InitialResIndicatorState());
  final logger = UnimedLogger(className: 'ResIndicatorsBloc');

  List<IndicatorModel> _listIndicators = [];
  List<IndicatorModel> get listIndicators => _listIndicators;

  @override
  Stream<ResIndicatorsState> mapEventToState(
    ResIndicatorEvent event,
  ) async* {
    if (event is GetListResIndicatorEvent) {
      try {
        yield LoadingResAllIndicatorsState();

        _listIndicators =
            await Locator.instance<ResApi>().resListIndicators(cpf: event.cpf);
        if (_listIndicators.isNotEmpty) {
          yield LoadedResIndicatorState(
            listResIndicators: _listIndicators,
          );
        } else {
          yield NoDataResIndicatorState();
        }
      } catch (e) {
        logger.e('Error on RequestIndicatorList Event $e');
        yield ErrorResIndicatorState(message: e.toString());
      }
    }
  }

  void searchCategoryIndicator({required List<String> indicators}) async {
    try {
      emit(LoadingResAllIndicatorsState());

      if (indicators.isEmpty) {
        emit(LoadedResIndicatorState(listResIndicators: _listIndicators));
        return;
      }

      if (indicators.last == 'Todos') {
        emit(LoadedResIndicatorState(listResIndicators: _listIndicators));
        return;
      }

      final List<IndicatorModel> filtredList = _listIndicators.where((element) {
        return indicators
            .any((indicator) => element.id.toString().contains(indicator));
      }).toList();
      emit(LoadedResIndicatorState(listResIndicators: filtredList));
    } catch (e) {
      emit(ErrorResIndicatorState(message: e.toString()));
    }
  }
}
