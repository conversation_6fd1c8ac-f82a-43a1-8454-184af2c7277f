// ignore_for_file: invalid_use_of_visible_for_testing_member

import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/indicators/indicator-data/indicators_data_event.dart';
import 'package:cliente_minha_unimed/bloc/res/indicators/indicator-data/indicators_data_state.dart';
import 'package:cliente_minha_unimed/models/res/res_indicator_data.model.dart';
import 'package:cliente_minha_unimed/shared/api/res.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';

class ResIndicatorsDataBloc
    extends Bloc<ResIndicatorsDataEvent, ResIndicatorsDataState> {
  ResIndicatorsDataBloc() : super(InitialResIndicatorsDataState());
  final logger = UnimedLogger(className: 'ResIndicatorDatasBloc');

  List<IndicatorDataModel> _listIndicatorsData = [];
  List<IndicatorDataModel> get listIndicatorsData => _listIndicatorsData;

  List<String> _indicatorsIds = [];

  DateTimeRange _dateRange = DateTimeRange(
    start: DateTime.now().subtract(Duration(days: 90)),
    end: DateTime.now(),
  );

  @override
  Stream<ResIndicatorsDataState> mapEventToState(
    ResIndicatorsDataEvent event,
  ) async* {
    if (event is GetResIndicatorsDataEvent) {
      try {
        yield LoadingResIndicatorsDataState();

        _indicatorsIds = event.indicatorsId;

         if(_indicatorsIds.isEmpty){
          yield NoDataResIndicatorDataState();
          return;
        } 
        _listIndicatorsData = await Locator.instance<ResApi>()
            .resIndicatorsData(
                cpf: event.cpf,
                indicatorsId: event.indicatorsId,
                dateRange: _dateRange);
        if (_listIndicatorsData.isNotEmpty) {
          yield LoadedResIndicatorDataState(
            resIndicatorsData: _listIndicatorsData,
          );
        } else {
          yield NoDataResIndicatorDataState();
        }
      } catch (e) {
        logger.e('Error on GetResIndicatorsDataEvent Event $e');
        yield ErrorResIndicatorDataState(message: e.toString());
      }
    } else if (event is UpdateDateRangeEvent) {
      try {
        yield LoadingResIndicatorsDataState();
        _dateRange = event.dateRange;

        _listIndicatorsData = await Locator.instance<ResApi>()
            .resIndicatorsData(
                cpf: event.cpf,
                indicatorsId: _indicatorsIds,
                dateRange: _dateRange);

        if (_listIndicatorsData.isNotEmpty) {
          yield LoadedResIndicatorDataState(
            resIndicatorsData: _listIndicatorsData,
          );
        } else {
          yield NoDataResIndicatorDataState();
        }
      } catch (e) {
        logger.e('Error on GetResIndicatorsDataEvent Event $e');
        yield ErrorResIndicatorDataState(message: e.toString());
      }
    }
  }
}
