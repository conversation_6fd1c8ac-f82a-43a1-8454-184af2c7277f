import 'package:cliente_minha_unimed/models/res/procedures/res_procedure_detail_model.dart';
import 'package:equatable/equatable.dart';

abstract class ResProcedureDetailState extends Equatable {
  const ResProcedureDetailState();

  @override
  List<Object> get props => [];
}

class InitialResProcedureDetailState extends ResProcedureDetailState {}

class LoadingResProcedureDetailState extends ResProcedureDetailState {}

class ErrorResProcedureDetailState extends ResProcedureDetailState {
  final String message;

  const ErrorResProcedureDetailState({required this.message});

  @override
  List<Object> get props => [message];
}

class LoadedResProcedureDetailState extends ResProcedureDetailState {
  final List<ResProcedureDetailModel> resProcedureDetailModel;

  const LoadedResProcedureDetailState({
    required this.resProcedureDetailModel,
  });

  @override
  List<Object> get props => [resProcedureDetailModel];
}

class LoadingResAllergiesSearchState extends ResProcedureDetailState {}

class ErrorResAllergiesSearchState extends ResProcedureDetailState {
  final String message;

  const ErrorResAllergiesSearchState({required this.message});

  @override
  List<Object> get props => [message];
}