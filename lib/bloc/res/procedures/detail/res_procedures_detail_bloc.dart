import 'package:cliente_minha_unimed/models/res/procedures/res_procedure_detail_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'res_procedures_detail_event.dart';
import 'res_procedures_detail_state.dart';

class ResProcedureDetailBloc extends Bloc<ResProcedureDetailEvent, ResProcedureDetailState> {
  ResProcedureDetailBloc() : super(LoadingResProcedureDetailState());

  List<ResProcedureDetailModel>? _resProcedureDetailModel;
  List<ResProcedureDetailModel>? get resProcedureDetailModel => _resProcedureDetailModel;

  @override
  Stream<ResProcedureDetailState> mapEventToState(ResProcedureDetailEvent event) async* {
    if (event is GetProcedureDetailEvent) {
      yield LoadingResProcedureDetailState();
      try {
     /*    _resProcedureDetailModel = await Locator.instance!<GraphQlApi>()
            .resProcedureDetail(crm: event.crm, card: event.card, code: event.code);          
 */

//OBS: TODO ESTE TRECHO DE CODIGO PARA EMISSAO DO ESTADO LOADED FOI APENAS PARA DESENVOLVER A TELA, SERA SUBSTITUIDO QUANDO O SERVIÇO FICAR PRONTO
    _resProcedureDetailModel = [];
   await Future.delayed(Duration(seconds: 3),(){
          _resProcedureDetailModel!.add(ResProcedureDetailModel(nomeProcedimento: "lorem ipson", nomeMedico: "nomeMedico", dataProcedimento: DateTime.now().toString()));
      _resProcedureDetailModel!.add(ResProcedureDetailModel(nomeProcedimento: "procedimento padrao", nomeMedico: "meedico novo", dataProcedimento: DateTime.now().toString()));
        _resProcedureDetailModel!.add(ResProcedureDetailModel(nomeProcedimento: "lorem ipson", nomeMedico: "nomeMedico", dataProcedimento: DateTime.now().toString()));
          _resProcedureDetailModel!.add(ResProcedureDetailModel(nomeProcedimento: "lorem ipson", nomeMedico: "nomeMedico", dataProcedimento: DateTime.now().toString()));
            _resProcedureDetailModel!.add(ResProcedureDetailModel(nomeProcedimento: "lorem ipson", nomeMedico: "nomeMedico", dataProcedimento: DateTime.now().toString()));
              _resProcedureDetailModel!.add(ResProcedureDetailModel(nomeProcedimento: "lorem ipson", nomeMedico: "nomeMedico", dataProcedimento: DateTime.now().toString()));
       
    });


     yield LoadedResProcedureDetailState(
            resProcedureDetailModel: _resProcedureDetailModel!);
      } catch (e) {
        yield ErrorResProcedureDetailState(message: e.toString());
      }
    }
  }
}