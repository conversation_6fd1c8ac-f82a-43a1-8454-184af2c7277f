import 'package:cliente_minha_unimed/bloc/agendamento/agendamento_bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento_event.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/medical-guide/medical-guide-search.model.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/models/schedule-provider.dart';
import 'package:cliente_minha_unimed/models/scheduling-consultation/permission-create-shedule.dart';
import 'package:cliente_minha_unimed/models/scheduling-consultation/schedule.dart';
import 'package:cliente_minha_unimed/screens/agendamento/main.dart';
import 'package:cliente_minha_unimed/screens/medical-guide/main.dart';
import 'package:cliente_minha_unimed/shared/i18n/i18n_helper.dart';
import 'package:cliente_minha_unimed/shared/screen_transitions/scale.transition.dart';
import 'package:cliente_minha_unimed/shared/screen_transitions/size.transition.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:cliente_minha_unimed/shared/widgets/buttons/action-button.dart';
import 'package:cliente_minha_unimed/shared/widgets/buttons/back-button.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/braco-cruzados.dart';
import 'package:cliente_minha_unimed/shared/widgets/info-tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

final String baseTranslate = 'schedulingConsultation';

void showScheduleSucessAlert(
  BuildContext screenContext,
  ScheduleProvider? provider,
  EspecialtyModel? speciality,
  ScheduleModel? schedule,
  AddressModel? address,
  String? protocolo,
  Function? onClose,
) {
  showDialog(
      context: screenContext,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
            canPop: true,
            child: AlertDialog(
                content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.check_circle,
                          size: 70.0, color: UnimedColors.greenLight)
                    ],
                  ),
                ),
                Padding(
                    padding: EdgeInsets.symmetric(vertical: 24),
                    child: Text(
                      I18nHelper.translate(
                          context, '$baseTranslate.data.alert'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: UnimedColors.grayDark2),
                    )),
                InfoTile(
                    label: I18nHelper.translate(
                        context, '$baseTranslate.data.field.protocol'),
                    value: protocolo),
                InfoTile(
                    label: I18nHelper.translate(
                        context, '$baseTranslate.data.field.speciality'),
                    value: speciality!.nomeEspecialidade),
                InfoTile(
                    label: I18nHelper.translate(
                        context, '$baseTranslate.data.field.providerName'),
                    value: provider!.nomePrestador),
                Row(
                  children: [
                    Expanded(
                        child: InfoTile(
                            label: I18nHelper.translate(
                                context, '$baseTranslate.data.field.date'),
                            value: schedule!.dataFormated)),
                    Expanded(
                        child: InfoTile(
                            label: I18nHelper.translate(
                                context, '$baseTranslate.data.field.schedule'),
                            value: schedule.horaFormated)),
                  ],
                ),
                InfoTile(
                    label: I18nHelper.translate(
                        context, '$baseTranslate.data.field.location'),
                    value: address!.adrressFormatted()),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  child: alertButton(
                      context: context,
                      text: 'CONTINUAR',
                      color: UnimedColors.green,
                      onClick: () => {
                            Navigator.pop(context),
                            if (onClose != null)
                              {
                                onClose(),
                              }

                            // Navigator.popUntil(
                            //     context, (route) => route.isFirst)
                          }),
                ),
              ],
            )));
      });
}

void showCreatedScheduleAlert(BuildContext context,
    PermissionCreateSheduleModel? permissionCreateSheduleModel) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(mainAxisAlignment: MainAxisAlignment.end, children: [
              GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Icon(Icons.close_outlined,
                      size: 32, color: UnimedColors.greenChart))
            ]),
            Padding(
                padding: EdgeInsets.only(bottom: 12),
                child:
                    Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                  Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                          color: UnimedColors.redStatus,
                          shape: BoxShape.circle),
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 50,
                      ))
                ])),
            Padding(
                padding: EdgeInsets.symmetric(vertical: 24),
                child: Text(
                  permissionCreateSheduleModel!.mensagem!,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: UnimedColors.grayDark2),
                )),
            InfoTile(
                label: I18nHelper.translate(
                    context, '$baseTranslate.data.field.protocol'),
                value: permissionCreateSheduleModel
                    .agendamentoExistente!.protocolo),
            InfoTile(
                label: I18nHelper.translate(
                    context, '$baseTranslate.data.field.speciality'),
                value: permissionCreateSheduleModel
                    .agendamentoExistente!.especialidade!.nomeEspecialidade),
            InfoTile(
                label: I18nHelper.translate(
                    context, '$baseTranslate.data.field.providerName'),
                value: permissionCreateSheduleModel
                    .agendamentoExistente!.prestador!.nomePrestador),
            Row(
              children: [
                Expanded(
                    child: InfoTile(
                        label: I18nHelper.translate(
                            context, '$baseTranslate.data.field.date'),
                        value: permissionCreateSheduleModel
                            .agendamentoExistente!.dataFormated)),
                Expanded(
                    child: InfoTile(
                        label: I18nHelper.translate(
                            context, '$baseTranslate.data.field.schedule'),
                        value: permissionCreateSheduleModel
                            .agendamentoExistente!.horaFormated)),
              ],
            ),
            permissionCreateSheduleModel.agendamentoExistente!.isTeleconsulta
                ? Container()
                : InfoTile(
                    label: I18nHelper.translate(
                        context, '$baseTranslate.data.field.location'),
                    value: permissionCreateSheduleModel
                        .agendamentoExistente!.local!
                        .adrressFormatted()),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: alertButton(
                context: context,
                text: permissionCreateSheduleModel
                        .agendamentoExistente!.isTeleconsulta
                    ? 'IR PARA TELECONSULTA'
                    : 'IR PARA AGENDAMENTO',
                color: const Color.fromRGBO(0, 153, 93, 1),
                onClick: () {
                  Navigator.pop(context);
                  if (permissionCreateSheduleModel
                      .agendamentoExistente!.isTeleconsulta) {
                    // Redireciona para a aba de teleconsulta
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => AgendamentoScreen(
                          tabIndex: 1,
                        ),
                      ),
                    );
                    BlocProvider.of<AgendamentoBloc>(context).add(ListarEvent(
                      perfil: BlocProvider.of<PerfilBloc>(context).perfil,
                    ));
                  } else {
                    BlocProvider.of<AgendamentoBloc>(context).add(
                      ListarEvent(
                        perfil: BlocProvider.of<PerfilBloc>(context).perfil,
                      ),
                    );
                    Navigator.pushReplacement(
                      context,
                      SizeRoute(
                        page: AgendamentoScreen(),
                      ),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      );
    },
  );
}

void showRedirectToMedicalGuideAlert(
  BuildContext context,
  String nomeEsepecialidade,
) {
  double height = MediaQuery.of(context).size.height;
  double dp = height / 100;
  showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
            insetPadding: EdgeInsets.all(0),
            child: Padding(
              padding: EdgeInsets.all(20),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        GestureDetector(
                          onTap: () => Navigator.of(context).pop(),
                          child: Icon(
                            Icons.close,
                            color: UnimedColors.green,
                          ),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 15,
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        children: [
                          Container(
                            padding: EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              border:
                                  Border.all(color: UnimedColors.grayLight2),
                              borderRadius: BorderRadius.all(
                                Radius.circular(5.0),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  nomeEsepecialidade,
                                  style: TextStyle(fontSize: 18),
                                ),
                                Icon(
                                  Icons.search,
                                  color: UnimedColors.green,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: dp * 10,
                          ),
                          Text(
                            I18nHelper.translate(context,
                                '$baseTranslate.speciality.redirectMedicalGuide.span1'),
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 18),
                          ),
                          SizedBox(
                            height: dp * 5,
                          ),
                          EvaBracosCruzados(
                            onTap: null,
                          ),
                          SizedBox(
                            height: dp * 5,
                          ),
                          Text(
                            I18nHelper.translate(context,
                                '$baseTranslate.speciality.redirectMedicalGuide.span2'),
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 18),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          ActionButton(
                            textButton: I18nHelper.translate(context,
                                '$baseTranslate.speciality.redirectMedicalGuide.buttons.action'),
                            onClick: () {
                              Navigator.push(
                                context,
                                ScaleRoute(
                                  page: MedicalGuideScreen(
                                    searchParams: SearchMedicalGuideModel(
                                      provider: nomeEsepecialidade,
                                    ),
                                    isFavoriteScreen: false,
                                  ),
                                ),
                              );
                            },
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          UnimedBackButton(
                            textButton: I18nHelper.translate(context,
                                '$baseTranslate.speciality.redirectMedicalGuide.buttons.back'),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ));
      });
}

alertButton(
    {BuildContext? context,
    required String text,
    Color? color,
    Function? onClick}) {
  return Container(
    height: 45,
    child: Material(
      child: InkWell(
        onTap: onClick as void Function()?,
        child: Ink(
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.all(
              Radius.circular(8.0),
            ),
          ),
          child: Center(
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Text(
                  text,
                  style: TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                )),
          ),
        ),
      ),
    ),
  );
}

void confirmSchedulingExit(BuildContext context) {
  bool canClickButton = true;
  List<Widget> _actions = [
    ElevatedButton(
        style: ElevatedButton.styleFrom(
          foregroundColor: Colors.white,
          backgroundColor: unimedOrange,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.0)),
        ),
        child:
            Text(I18nHelper.translate(context, '$baseTranslate.exit.confirm')),
        onPressed: () => {
              if (canClickButton)
                {
                  canClickButton = false,
                  Navigator.of(context).pop(),
                  Navigator.of(context).pop()
                }
            }),
  ];

  Alert.open(
    context,
    title: I18nHelper.translate(context, '$baseTranslate.exit.title'),
    text: I18nHelper.translate(context, '$baseTranslate.exit.text'),
    actions: _actions,
    textButtonClose: I18nHelper.translate(context, 'common.cancel'),
  );
}
