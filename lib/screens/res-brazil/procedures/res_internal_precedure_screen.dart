// ignore_for_file: unnecessary_null_comparison

import 'package:cliente_minha_unimed/bloc/res/procedures/detail/res_procedures_detail_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/procedures/detail/res_procedures_detail_event.dart';
import 'package:cliente_minha_unimed/bloc/res/procedures/detail/res_procedures_detail_state.dart';
import 'package:cliente_minha_unimed/bloc/res/procedures/res_procedures_bloc.dart';
import 'package:cliente_minha_unimed/bloc/res/procedures/res_procedures_event.dart';
import 'package:cliente_minha_unimed/bloc/res/procedures/res_procedures_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/procedures/res_procedure_detail.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/widget/app_bar_res.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/widget/empty_list.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/widget/erro_service.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/widget/expandable_card.dart';
import 'package:cliente_minha_unimed/screens/res-brazil/widget/filters_widget.dart';
import 'package:cliente_minha_unimed/shared/utils/router-observer.dart';
import 'package:cliente_minha_unimed/shared/widgets/eva/triste-refreshable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ResInternalProceduresScreen extends StatefulWidget {
  final String nameBeneficiary;
  final String beneficiaryCard;
  const ResInternalProceduresScreen(
      {super.key,
      required this.nameBeneficiary,
      required this.beneficiaryCard});

  @override
  State<ResInternalProceduresScreen> createState() =>
      _ResInternalAllergiesScreenState();
}

class _ResInternalAllergiesScreenState
    extends State<ResInternalProceduresScreen> with RouteAware {
  final bool _isLoading = false;
  bool loadingDetail = false;
  int? _filterMonthSelected = 0;
  DateTimeRange? _dateRangeToFilter;
  final List<int> _lastMonthsToFilter = [3, 6];

  List<ExpansionTileController> controllers = [];
  int? previusSelectedIndex;

  @override
  void initState() {
    _filterMonthSelected = 3;
    _dateRangeToFilter = DateTimeRange(
      start: _selectedDataStart(3),
      end: DateTime.now(),
    );
    _loadProcedures();
    context.read<ResProceduresBloc>().add(ListResProceduresEvent(
          crm: '123456',
          card: widget.beneficiaryCard,
          dataRange: _dateRangeToFilter,
        ));

    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: UnimedColors.white,
      appBar: AppBarRes(
        title: 'Procedimentos',
        nameBeneficiary: widget.nameBeneficiary,
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 24),
        child: Column(
          children: [
            _filterDate(),
            BlocBuilder<ResProceduresBloc, ResProceduresState>(
              builder: (context, state) {
                if (state is LoadingResProceduresState) {
                  return Expanded(
                    child: Center(
                      child: SpinKitCircle(color: UnimedColors.green),
                    ),
                  );
                } else if (state is LoadedResProceduresState) {
                  return Expanded(
                      child: RefreshIndicator(
                          onRefresh: () async {
                            _loadProcedures();
                          },
                          child: ListView.builder(
                            physics: const ClampingScrollPhysics(
                                parent: AlwaysScrollableScrollPhysics()),
                            itemCount: state.listResProcedures.length,
                            itemBuilder: (context, index) {
                              ExpansionTileController controller =
                                  ExpansionTileController();

                              controllers.add(controller);
                              return Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: ExpandableCard(
                                  controller: controller,
                                  pathIcon: 'assets/svg/icon_procedure.svg',
                                  title: 'Procedimento',
                                  subtitle: state.listResProcedures[index]
                                      .dataEntradaFormatted,
                                  loading: loadingDetail,
                                  onExpansionChanged: (value) {
                                    setState(() {
                                      if (value &&
                                          previusSelectedIndex != index) {
                                        if (previusSelectedIndex != null) {
                                          controllers[previusSelectedIndex!]
                                              .collapse();
                                        }
                                        previusSelectedIndex = index;
                                      }
                                    });
                                  },
                                  //TODO: Implementar botão de procedimentos quando o serviço for disponibilizado
                                  /*   buttons: [
                                    CustomButtonCard(
                                      text: 'Procedimentos',
                                      onPressed: () {
                                         context.read<ResProcedureDetailBloc>().add(GetProcedureDetailEvent(
                                          crm: '123'/* context.read<Auth>().credentials.crm */,
                                          card: widget.beneficiaryCard,
                                          code: state.listResProcedures[index].code ?? '',
                                          index: index,

                                        ));

                                        _showDetailModal(state.listResProcedures[index].code ?? '');
                                         
                                      },
                                    ),
                                  ], */
                                  additionalInfo: [
                                    {
                                      'title': 'Tipo',
                                      'description': state
                                              .listResProcedures[index]
                                              .typeProcedure ??
                                          'Sem dados no momento.'
                                    },
                                    {
                                      'title': 'Local',
                                      'description': state
                                              .listResProcedures[index]
                                              .localName ??
                                          'Sem dados no momento.'
                                    },
                                  ],
                                ),
                              );
                            },
                          )));
                } else if (state is NoDataResProceduresSearchState) {
                  return Expanded(
                    child: EmptyList(
                      pathIcon: 'assets/svg/icon_file.svg',
                      message:  'Sem dados de prodecimentos.',
                    ),
                  );
                } else if (state is ErrorResProceduresState) {
                  return Center(
                    child: EvaTristeRefreshable(
                      message: Text(state.message.toString()),
                      onRefresh: () {
                        context
                            .read<ResProceduresBloc>()
                            .add(ListResProceduresEvent(
                              crm: '123456',
                              card: widget.beneficiaryCard,
                              dataRange: _dateRangeToFilter,
                            ));
                        _loadProcedures();
                      },
                    ),
                  );
                }
                return Container();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _filterDate() {
    return FiltersWidget(
      isLoading: _isLoading,
      lastMonthsToFilter: _lastMonthsToFilter,
      filterMonthSelected: _filterMonthSelected,
      dateRangeToFilter: _dateRangeToFilter,
      onMonthFilterChanged: (filterSelected) {
        setState(() {
          _filterMonthSelected = filterSelected;
          filterSelected == null
              ? _dateRangeToFilter = null
              : _dateRangeToFilter = DateTimeRange(
                  start: _selectedDataStart(filterSelected),
                  end: DateTime.now(),
                );
        });
        _loadProcedures();
      },
      onClearDateRange: () {
        setState(() {
          _dateRangeToFilter = null;
          _filterMonthSelected = null;
        });
        _loadProcedures();
      },
      selectDateToFilter: _selectDateToFilter,
      onDateRangeSelected: (dateRange) {
        setState(() {
          _filterMonthSelected = null;
          _dateRangeToFilter = dateRange;
        });
        _loadProcedures();
      },
    );
  }

  //esse metodo sera usado novamenet assim que o servico de detalhes de procedimentos estiver pronto
  void _showDetailModal(String code) {
    showModalBottomSheet<void>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
      ),
      builder: (BuildContext context) {
        return BlocProvider(
          create: (context) => ResProcedureDetailBloc()
            ..add(GetProcedureDetailEvent(
              card: widget.beneficiaryCard,
            )),
          child: BlocBuilder<ResProcedureDetailBloc, ResProcedureDetailState>(
            builder: (context, state) {
              if (state is LoadingResProcedureDetailState) {
                return Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Center(
                      child: SpinKitCircle(color: UnimedColors.green),
                    ),
                    const SizedBox(height: 16),
                  ],
                );
              } else if (state is LoadedResProcedureDetailState) {
                return Expanded(
                  child: ResProcedureDetail(
                    resProcedureDetailModel: state.resProcedureDetailModel,
                  ),
                );
              } else if (state is ErrorResProcedureDetailState) {
                return Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Center(
                    child: ErroService(
                      message: state.message,
                      onPressed: () {
                        _loadProcedures();
                      },
                    ),
                  ),
                );
              } else {
                return Container();
              }
            },
          ),
        );
      },
    );
  }

  DateTime _getFirstDate() {
    DateTime now = DateTime.now();
    int year = now.year;
    bool isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    return now.subtract(Duration(days: isLeapYear ? 366 : 365));
  }

  Future<DateTime?> _selectDateToFilter(BuildContext context, String helpText,
      {DateTime? firstDate}) async {
    return await showDatePicker(
      context: context,
      helpText: helpText,
      initialDate: DateTime.now(),
      firstDate: firstDate ?? _getFirstDate(),
      lastDate: DateTime.now(),
      keyboardType: TextInputType.text,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(
              primary: UnimedColors.green,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );
  }

  _loadProcedures() {
    setState(() {
      controllers = [];
      previusSelectedIndex = null;
    });

    context.read<ResProceduresBloc>().add(ListResProceduresEvent(
          crm: '123456',
          card: widget.beneficiaryCard,
          dataRange: _dateRangeToFilter,
        ));
  }

  DateTime _selectedDataStart(int meses) {
    DateTime dataAtual = DateTime.now();
    DateTime novaData = DateTime(
      dataAtual.year,
      dataAtual.month - meses,
      dataAtual.day,
    );

    return novaData;
  }
}
